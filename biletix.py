import re
import time
from typing import Any, Dict, List, Optional

import pandas as pd
from config import BiletixConfig
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import WebDriverWait


class BiletixScraper:
    """Web scraper for Biletix.com events."""

    def __init__(self):
        self.config = BiletixConfig()
        self.driver = webdriver.Firefox()
        self.wait = WebDriverWait(self.driver, self.config.DEFAULT_WAIT_TIMEOUT)

    def __del__(self):
        """Ensure driver is closed when object is destroyed."""
        try:
            self.driver.quit()
        except Exception:
            pass

    def scrape_page(self, link: str | List[str]) -> pd.DataFrame:
        """Main scraping method that handles both single URLs and lists of URLs."""
        if isinstance(link, list):
            df_list = [self.scrape_page(l) for l in link]
            return pd.concat(df_list, ignore_index=True)

        df = pd.DataFrame(columns=self.config.DATA_COLUMNS)

        try:
            self.driver.get(link)

            # Wait for the main div element to be present
            main_div = self.wait.until(EC.presence_of_element_located((By.ID, "all_result")))
            time.sleep(2)

            old_length = 0
            while True:
                child_items = main_div.find_elements(
                    By.CSS_SELECTOR,
                    "div.grid_21.alpha.omega.listevent.searchResultEvent a.ln1.searchResultEventName",
                )
                child_links = [item.get_attribute("href") for item in child_items]

                for link in child_links[old_length:1]:  # Process only first item for testing
                    print(link)
                    try:
                        self.driver.execute_script(f"window.open('{link}', '_blank');")

                        # Wait for the new tab to open and switch to it
                        self.wait.until(EC.new_window_is_opened)
                        time.sleep(0.75)

                        self.driver.switch_to.window(self.driver.window_handles[-1])
                        self.wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, "h1.btx-title")))
                        time.sleep(0.75)

                        if "/etkinlik/" in self.driver.current_url:
                            try:
                                performance_links = self._extract_performances_from_events()
                            except Exception:
                                continue
                        else:
                            try:
                                event_links = self._extract_events_from_event_groups()

                                performance_links = []
                                for event_link in event_links:
                                    self.driver.execute_script(f"window.open('{event_link}', '_blank');")

                                    self.wait.until(EC.new_window_is_opened)
                                    time.sleep(0.75)

                                    self.driver.switch_to.window(self.driver.window_handles[-1])
                                    self.wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, "h1.btx-title")))
                                    time.sleep(0.75)

                                    performance_links.extend(self._extract_performances_from_events())

                                    self.driver.close()
                                    self.driver.switch_to.window(self.driver.window_handles[-1])
                                    time.sleep(0.75)
                            except Exception:
                                continue

                        for performance_link in performance_links:

                            driver.execute_script(f"window.open('{performance_link}', '_blank');")

                            wait.until(EC.new_window_is_opened)
                            time.sleep(0.75)

                            driver.switch_to.window(driver.window_handles[-1])
                            wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, "h1.btx-title")))
                            time.sleep(0.75)

                            if "/etkinlik/" in driver.current_url:
                                driver.close()
                                driver.switch_to.window(driver.window_handles[-1])
                                time.sleep(0.75)
                                continue

                            # Scrape data from the new page
                            page_data = {}
                            page_data["website_url"] = driver.current_url  # Page URL

                            try:
                                page_data["thumbnail_url"] = driver.find_element(
                                    By.CSS_SELECTOR, "btx-event-media img"
                                ).get_attribute("src")
                            except:
                                page_data["thumbnail_url"] = None

                            try:
                                page_data["event_name"] = driver.find_element(By.CSS_SELECTOR, "h1.btx-title").text
                            except:
                                page_data["event_name"] = None

                            try:
                                page_data["date"] = driver.find_element(
                                    By.CSS_SELECTOR, "div.perf-date.ng-star-inserted span"
                                ).text
                            except:
                                page_data["date"] = None

                            try:
                                page_data["time"] = driver.find_element(
                                    By.CSS_SELECTOR, "div.perf-time.ng-star-inserted span"
                                ).text
                            except:
                                page_data["time"] = None

                            try:
                                page_data["location_name"] = driver.find_element(
                                    By.CSS_SELECTOR,
                                    "div.event-location.ng-star-inserted span",
                                ).text
                            except:
                                page_data["location_name"] = None

                            try:
                                page_data["location_url"] = driver.find_element(
                                    By.CSS_SELECTOR,
                                    "div.event-location.ng-star-inserted a",
                                ).get_attribute("href")
                            except:
                                page_data["location_url"] = None

                            try:
                                if page_data["location_url"] is not None:
                                    if page_data["location_url"] in df["location_url"].values:
                                        page_data["open_address"] = df.loc[
                                            df["location_url"] == page_data["location_url"]
                                        ]["open_address"].values[0]
                                    else:
                                        driver.execute_script(f"window.open('{page_data["location_url"]}', '_blank');")

                                        wait.until(EC.new_window_is_opened)
                                        time.sleep(0.75)

                                        driver.switch_to.window(driver.window_handles[-1])
                                        wait.until(
                                            EC.presence_of_element_located((By.XPATH, "//ul[@itemprop='address']"))
                                        )
                                        time.sleep(0.75)

                                        address = driver.find_element(
                                            By.XPATH,
                                            "//ul[@itemprop='address']",
                                        )
                                        page_data["open_address"] = address.text

                                        driver.close()
                                        driver.switch_to.window(driver.window_handles[-1])
                                        time.sleep(0.75)
                                else:
                                    page_data["open_address"] = None
                            except:
                                page_data["open_address"] = None

                            try:
                                page_data["description"] = driver.find_element(
                                    By.CSS_SELECTOR,
                                    "btx-about-event.ng-star-inserted > div.about-event-container div",
                                ).text
                            except:
                                page_data["description"] = None

                            # Append page data to the DataFrame
                            df.loc[len(df.index)] = page_data

                            driver.close()
                            driver.switch_to.window(driver.window_handles[-1])
                            time.sleep(0.75)

                        driver.close()
                        driver.switch_to.window(driver.window_handles[-1])
                        time.sleep(0.75)

                        if len(df.index) >= MAX_LIST_SIZE:
                            return df
                    except:
                        handles = driver.window_handles
                        for handle in handles[1:]:
                            driver.switch_to.window(handle)
                            driver.close()
                            time.sleep(0.75)
                        driver.switch_to.window(handles[0])
                        time.sleep(0.75)

                try:
                    button = driver.find_element(By.CSS_SELECTOR, "a.search_load_more")
                    button.click()
                    time.sleep(2)
                except:
                    break

                old_length = len(child_items)

            driver.quit()
            return df
        except:
            return df


def extract_performances_from_events(driver):
    performances_list = driver.find_element(By.TAG_NAME, "btx-performance-list")
    performances = performances_list.find_elements(By.TAG_NAME, "btx-list-item")
    performance_links = []
    for performance in performances:
        try:
            if performance.find_element(By.TAG_NAME, "mat-basic-chip").text != "Satışta":
                continue
            performance_links.append(
                performance.find_element(By.CSS_SELECTOR, "a.ng-star-inserted").get_attribute("href")
            )
        except:
            pass
    return performance_links


def extract_events_from_event_groups(driver):
    events_list = driver.find_element(By.TAG_NAME, "btx-event-group-list")
    events = events_list.find_elements(By.TAG_NAME, "btx-list-item")
    event_links = []
    for event in events:
        try:
            if event.find_element(By.TAG_NAME, "mat-basic-chip").text != "Satışta":
                continue
            event_links.append(event.find_element(By.TAG_NAME, "a").get_attribute("href"))
        except:
            pass
    return event_links


# Set up Selenium WebDriver
driver = webdriver.Firefox()
links = ["https://www.biletix.com/search/TURKIYE/tr?category_sb=-1&date_sb=-1&city_sb=-1"]
scraped_df = scrape_page(driver, links)
scraped_df.to_csv("data/biletix.csv", index=False, sep=";", encoding="utf-8-sig")
