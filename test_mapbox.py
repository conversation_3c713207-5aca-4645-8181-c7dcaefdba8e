import json
import os
import sys
from typing import Dict, Optional

# Add the parent directory to sys.path to import the modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from locations import MapboxClient


def test_get_location_information():
    """Test the get_location_information method of MapboxClient."""
    client = MapboxClient()

    # Test coordinates
    test_coordinates = "41.0460612,28.9878414"  # Latitude, Longitude format

    print(f"Testing get_location_information with coordinates: {test_coordinates}")
    print("Note: Coordinates should be in 'latitude,longitude' format here.")

    # Call the method
    result = client.get_location_information(test_coordinates)

    # Print the result
    print("\nResult:")
    print(json.dumps(result, indent=2))

    # Check if we got meaningful data
    if result["place_id"] is not None:
        print("\n✅ Test passed: Received place_id")
    else:
        print("\n❌ Test failed: No place_id received")

    if result["city"] is not None:
        print(f"✅ Test passed: City identified as '{result['city']}'")
    else:
        print("❌ Test failed: No city identified")

    if result["district"] is not None:
        print(f"✅ Test passed: District identified as '{result['district']}'")
    else:
        print("❌ Test failed: No district identified")

    return result


if __name__ == "__main__":
    print("Running basic test:")
    test_get_location_information()
