"""
Configuration constants for the IVent data scraping project.
"""

import os


class DatabaseConfig:
    """Database connection configuration."""

    DB_HOST = "ivent-test-umutbulbul-fec6.c.aivencloud.com"
    DB_DATABASE = "defaultdb"
    DB_USERNAME = "avnadmin"
    DB_PASSWORD = "AVNS_M7hBzpIRXoBCmI4Tv-u"
    DB_PORT = 22222
    CONNECTION_STRING = (
        f"DRIVER={{PostgreSQL Unicode}};"
        f"SERVER={DB_HOST};"
        f"DATABASE={DB_DATABASE};"
        f"UID={DB_USERNAME};"
        f"PWD={DB_PASSWORD};"
        f"PORT={DB_PORT};"
        f"SSLmode=require;"
        f"SSLrootcert=ca.pem;"
    )


class MapboxConfig:
    """Mapbox API configuration."""

    API_KEY = "pk.eyJ1IjoiYWxrbmRvb20iLCJhIjoiY21hYWVxdHhsMXY2MTJxcXpyZXVlMDNndCJ9.RdfBfDEnDxSWuPAfiFIwlQ"


class FilePathConfig:
    """File path configurations."""

    DATA_DIR = "./data"

    # Input files
    PASSO_CSV = os.path.join(DATA_DIR, "passo.csv")
    BILETIX_CSV = os.path.join(DATA_DIR, "biletix.csv")
    LOCATIONS_CSV = os.path.join(DATA_DIR, "locations.csv")
    PASSO_EDITED_CSV = os.path.join(DATA_DIR, "passo_edited.csv")
    IVENT_DATES_CSV = os.path.join(DATA_DIR, "ivent_dates.csv")
    VIBE_FOLDERS_CSV = os.path.join(DATA_DIR, "vibe_folders.csv")
    MEMORY_FOLDERS_CSV = os.path.join(DATA_DIR, "memory_folders.csv")

    # Output files
    INSERT_PASSO_SQL = os.path.join(DATA_DIR, "insert_passo_edited.sql")
    INSERT_LOCATIONS_SQL = os.path.join(DATA_DIR, "insert_locations.sql")
    INSERT_IVENT_DATES_SQL = os.path.join(DATA_DIR, "insert_ivent_dates.sql")
    INSERT_VIBE_FOLDERS_SQL = os.path.join(DATA_DIR, "insert_vibe_folders.sql")
    INSERT_MEMORY_FOLDERS_SQL = os.path.join(DATA_DIR, "insert_memory_folders.sql")


class ScrapingConfig:
    """General scraping configuration."""

    DEFAULT_WAIT_TIMEOUT = 10
    DEFAULT_MAX_LIST_SIZE = 1000

    # Common data columns for event scraping
    EVENT_DATA_COLUMNS = [
        "website_url",
        "thumbnail_url",
        "event_name",
        "date",
        "location_name",
        "location_url",
        "open_address",
        "description",
    ]


class PassoConfig(ScrapingConfig):
    """Passo.com.tr specific configuration."""

    OUTPUT_FILE = FilePathConfig.PASSO_CSV
    DATA_COLUMNS = [
        "div_id",
        "website_url",
        "thumbnail_url",
        "event_name",
        "date",
        "location_name",
        "location_url",
        "open_address",
        "coordinates",
        "description",
    ]

    SCRAPING_URLS = [
        "https://www.passo.com.tr/tr",
        "https://www.passo.com.tr/tr/kategori/muzik-konser-festival-biletleri/8615",
        "https://www.passo.com.tr/tr/kategori/futbol-mac-biletleri/4615",
        "https://www.passo.com.tr/tr/kategori/performans-sanatlari-tiyatro-dans-standup-muzikal-bilet/11615",
        "https://www.passo.com.tr/tr/kategori/basketbol-mac-biletleri/13615",
        "https://www.passo.com.tr/tr/kategori/muze-tarihi-mekan-saray-giris-biletleri/15615",
        "https://www.passo.com.tr/tr/kategori/diger-etkinlik-biletleri/12615",
    ]


class BiletixConfig(ScrapingConfig):
    """Biletix.com specific configuration."""

    OUTPUT_FILE = FilePathConfig.BILETIX_CSV
    DATA_COLUMNS = [
        "website_url",
        "thumbnail_url",
        "event_name",
        "date",
        "time",
        "location_name",
        "location_url",
        "open_address",
        "description",
    ]

    SCRAPING_URLS = ["https://www.biletix.com/search/TURKIYE/tr?category_sb=-1&date_sb=-1&city_sb=-1"]


class DataProcessingConfig:
    """Configuration for data processing operations."""

    # Date mapping for Turkish month abbreviations
    TURKISH_MONTH_MAP = {
        "OCA": "01",
        "ŞUB": "02",
        "MAR": "03",
        "NİS": "04",
        "MAY": "05",
        "HAZ": "06",
        "TEM": "07",
        "AĞU": "08",
        "EYL": "09",
        "EKI": "10",
        "KAS": "11",
        "ARA": "12",
    }

    # Default distributor ID for events
    DEFAULT_CREATOR_DISTRIBUTOR_ID = "02a0be1a-f0d8-43bf-9f6e-c9a61c33f739"
