Stack trace:
Frame         Function      Args
0007FFFF9CF0  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF8BF0) msys-2.0.dll+0x2118E
0007FFFF9CF0  0002100469BA (000000000000, 000000000000, 000000000000, 0007FFFF9FC8) msys-2.0.dll+0x69BA
0007FFFF9CF0  0002100469F2 (00021028DF99, 0007FFFF9BA8, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFF9CF0  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFF9CF0  00021006A545 (0007FFFF9D00, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0007FFFF9FD0  00021006B9A5 (0007FFFF9D00, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF92B160000 ntdll.dll
7FF92A1E0000 KERNEL32.DLL
7FF928910000 KERNELBASE.dll
7FF92A4E0000 USER32.dll
7FF9282B0000 win32u.dll
000210040000 msys-2.0.dll
7FF92AF50000 GDI32.dll
7FF9285B0000 gdi32full.dll
7FF928D00000 msvcp_win.dll
7FF928DB0000 ucrtbase.dll
7FF92A2F0000 advapi32.dll
7FF929D20000 msvcrt.dll
7FF92A430000 sechost.dll
7FF929720000 RPCRT4.dll
7FF9278E0000 CRYPTBASE.DLL
7FF928870000 bcryptPrimitives.dll
7FF92A2B0000 IMM32.DLL
