import os
import re
import time
from typing import Any, Dict, List, Optional, Tuple

import pandas as pd
from config import PassoConfig
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import WebDriverWait
from selenium.common.exceptions import WebDriverException, TimeoutException


class PassoScraper:
    """Web scraper for Passo.com.tr events with session recovery."""

    def __init__(self):
        self.config = PassoConfig()
        self.driver = None
        self.wait = None
        self.existing_data = self._load_existing_data()
        self.existing_ids = self.existing_data["div_id"].values.tolist()
        self.current_url = None
        self.last_processed_id = None

    def _init_driver(self):
        """Initialize or reinitialize the driver."""
        if self.driver:
            try:
                self.driver.quit()
            except Exception:
                pass
        
        self.driver = webdriver.Firefox()
        self.wait = WebDriverWait(self.driver, self.config.DEFAULT_WAIT_TIMEOUT)

    def _load_existing_data(self) -> pd.DataFrame:
        """Load existing scraped data if available."""
        if os.path.exists(self.config.OUTPUT_FILE):
            return pd.read_csv(self.config.OUTPUT_FILE, sep=";", encoding="utf-8-sig")
        return pd.DataFrame(columns=self.config.DATA_COLUMNS)

    def _save_progress(self, div_id: int, url: str):
        """Save current progress to a temporary file."""
        progress_data = {
            'last_processed_id': div_id,
            'current_url': url,
            'timestamp': time.time()
        }
        
        progress_file = 'scraper_progress.txt'
        with open(progress_file, 'w') as f:
            f.write(f"{div_id}|{url}|{progress_data['timestamp']}")

    def _load_progress(self) -> Tuple[Optional[int], Optional[str]]:
        """Load progress from temporary file."""
        progress_file = 'scraper_progress.txt'
        if os.path.exists(progress_file):
            try:
                with open(progress_file, 'r') as f:
                    content = f.read().strip()
                    if content:
                        parts = content.split('|')
                        if len(parts) >= 2:
                            return int(parts[0]), parts[1]
            except Exception:
                pass
        return None, None

    def _navigate_to_last_position(self, target_url: str, last_id: int) -> bool:
        """Navigate back to the last processed position."""
        try:
            print(f"Attempting to recover session. Going to: {target_url}")
            self.driver.get(target_url)
            
            # Wait for the main div element to be present
            main_div = self.wait.until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "div.wrapper.row.event-search-items"))
            )
            time.sleep(1)
            
            # Load items until we find our last processed ID
            old_length = 0
            max_attempts = 50  # Prevent infinite loop
            attempts = 0
            
            while attempts < max_attempts:
                child_items = main_div.find_elements(By.XPATH, "./div[@id]")
                
                # Check if we found our target ID
                for item in child_items:
                    div_id = int(item.get_attribute("id"))
                    if div_id == last_id:
                        print(f"Successfully recovered to position: ID {last_id}")
                        return True
                
                # Try to load more items
                if not self._load_more():
                    print("No more items to load during recovery.")
                    break
                    
                # Check if new items were loaded
                new_length = len(child_items)
                if new_length == old_length:
                    attempts += 1
                else:
                    attempts = 0  # Reset if we're making progress
                    
                old_length = new_length
                time.sleep(1)
            
            print(f"Could not find last processed ID {last_id}. Starting from current position.")
            return False
            
        except Exception as e:
            self._print_error(e)
            return False

    def _handle_session_error(self, target_url: str, last_id: int = None) -> bool:
        """Handle session errors by reinitializing driver and recovering position."""
        print("Session error detected. Attempting recovery...")
        
        try:
            self._init_driver()
            
            if last_id:
                return self._navigate_to_last_position(target_url, last_id)
            else:
                # Just navigate to the main page
                self.driver.get(target_url)
                self.wait.until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, "div.wrapper.row.event-search-items"))
                )
                time.sleep(1)
                return True
                
        except Exception as e:
            self._print_error(e)
            return False

    def _get_thumbnail_url(self) -> Optional[str]:
        """Extract thumbnail URL from event page."""
        try:
            return self.driver.find_element(By.CSS_SELECTOR, "img.detail-img").get_attribute("src")
        except Exception:
            return None

    def _get_event_name(self) -> Optional[str]:
        """Extract event name from event page."""
        try:
            return self.driver.find_element(By.CSS_SELECTOR, "div.font-weight-bold.ellipsis").text
        except Exception:
            return None

    def _get_date(self) -> Optional[str]:
        """Extract event date from event page with multiple fallback options."""
        date_selectors = [
            "div.main-slider-item div.text-white",
            "div.main-slider-item div.text-uppercase",
            "div.col-md-4.ticket-info ul li",
        ]

        for selector in date_selectors:
            try:
                date_text = self.driver.find_element(By.CSS_SELECTOR, selector).text
                if date_text:
                    return date_text
            except Exception:
                continue
        return None

    def _get_location_name(self) -> Optional[str]:
        """Extract location name from event page with fallback options."""
        location_selectors = ["div.box.cursor-pointer h4 div", "div.box h4 div.text-primary.my-2.cursor-pointer"]

        for selector in location_selectors:
            try:
                location_name = self.driver.find_element(By.CSS_SELECTOR, selector).text
                if location_name:
                    return location_name
            except Exception:
                continue
        return None

    def _get_coordinates(self) -> Optional[str]:
        """Extract coordinates from Google Maps link."""
        try:
            href = self.driver.find_element(By.CSS_SELECTOR, "div.box.cursor-pointer h4 a").get_attribute("href")
            match = re.search(r"q=(\d{1,2}\.?\d*,\d{1,2}\.?\d*)", href)
            return match.group(1) if match else None
        except Exception:
            return None

    def _get_description(self) -> Optional[str]:
        """Extract and clean event description."""
        try:
            event_description_wrapper = self.driver.find_element(By.CSS_SELECTOR, "div.event-description-wrapper").text
            description = re.sub(r"\<.*?\>", "\n", event_description_wrapper).strip()
            return re.sub(r"\n+", "\n", description)
        except Exception:
            return None

    def _get_location_url_and_open_address(self) -> Tuple[Optional[str], Optional[str]]:
        """Extract location URL and open address by navigating to location page."""
        location_selectors = ["div.box.cursor-pointer h4 div", "div.box h4 div.text-primary.my-2.cursor-pointer"]

        location_element = None
        for selector in location_selectors:
            try:
                location_element = self.driver.find_element(By.CSS_SELECTOR, selector)
                break
            except Exception:
                continue

        if location_element is None:
            return None, None

        try:
            self.driver.execute_script("arguments[0].click();", location_element)

            # Wait for the new tab to open and switch to it
            self.wait.until(
                EC.presence_of_element_located(
                    (By.CSS_SELECTOR, "h1.text-red.font-weight-bold.font-size-1875.mt-5.mb-3")
                )
            )
            print("Current Url: ", self.driver.current_url)
            time.sleep(0.75)

            location_url = self.driver.current_url
            try:
                open_address = self.driver.find_element(By.CSS_SELECTOR, "a.address").text
            except Exception:
                open_address = None

            return location_url, open_address
        except Exception as e:
            self._print_error(e)
            return None, None

    def _switch_back_to_main_page(self):
        """Close all tabs except the main one and switch back to it."""
        print("Switching back to the main page.")
        try:
            handles = self.driver.window_handles
            for handle in handles[1:]:
                self.driver.switch_to.window(handle)
                self.driver.close()
                time.sleep(0.75)
            self.driver.switch_to.window(handles[0])
            print("Current Url: ", self.driver.current_url)
        except Exception as e:
            self._print_error(e)

    def _load_more(self) -> bool:
        """Try to click 'Load More' button to get additional events."""
        try:
            button = self.driver.find_element(By.XPATH, "//button[text()=' Daha Fazla Göster']")
            print("Trying to click 'Daha Fazla Göster' button.")
            self.driver.execute_script("arguments[0].click();", button)
            print("Clicked 'Daha Fazla Göster' button.")
            time.sleep(1.5)
            return True
        except Exception:
            return False

    def _print_error(self, e: Exception):
        """Print formatted error message."""
        print(f"\n{'#' * 40}\nError: {e}\n{'#' * 40}\n")

    def _scrape_event(self) -> Dict[str, Any]:
        """Scrape data from a single event page."""
        # Wait for the event page to load
        self.wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, "div.page-breadcrumb")))
        print("Current Url: ", self.driver.current_url)
        time.sleep(0.75)

        # Scrape data from the new page
        page_data = {
            "website_url": self.driver.current_url,
            "thumbnail_url": self._get_thumbnail_url(),
            "event_name": self._get_event_name(),
            "date": self._get_date(),
            "location_name": self._get_location_name(),
            "coordinates": self._get_coordinates(),
            "description": self._get_description(),
        }

        location_url, open_address = self._get_location_url_and_open_address()
        page_data["location_url"] = location_url
        page_data["open_address"] = open_address

        return page_data

    def scrape(self, link: str) -> pd.DataFrame:
        """Main scraping method for a given URL with session recovery."""
        df = pd.DataFrame(columns=self.config.DATA_COLUMNS)
        
        # Load previous progress
        last_processed_id, last_url = self._load_progress()
        
        # Initialize driver
        self._init_driver()
        
        max_retries = 3
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                # If we have previous progress and it's the same URL, try to recover
                if last_processed_id and last_url == link and retry_count == 0:
                    if not self._navigate_to_last_position(link, last_processed_id):
                        # If recovery fails, start fresh
                        self.driver.get(link)
                else:
                    self.driver.get(link)

                # Wait for the main div element to be present
                main_div = self.wait.until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, "div.wrapper.row.event-search-items"))
                )
                time.sleep(0.75)

                old_length = 0
                processed_in_session = 0
                
                while True:
                    try:
                        child_items = main_div.find_elements(By.XPATH, "./div[@id]")

                        for item in child_items[old_length:]:
                            div_id = int(item.get_attribute("id"))
                            print(f"\nEvent Item Id: {div_id}")

                            # Skip if we're recovering and haven't reached our last position yet
                            if last_processed_id and div_id <= last_processed_id and retry_count == 0:
                                print(f"Skipping ID {div_id} - already processed or before last position")
                                continue

                            if div_id in self.existing_ids:
                                print(f"ID already exists. Skipping.")
                                continue

                            try:
                                # Click on the event item
                                self.driver.execute_script("arguments[0].click();", item)

                                # Wait for the new tab to open and switch to it
                                self.wait.until(EC.new_window_is_opened)
                                time.sleep(0.75)
                                self.driver.switch_to.window(self.driver.window_handles[-1])

                                if "etkinlik-grubu" not in self.driver.current_url:
                                    # Append page data to the DataFrame
                                    page_data = self._scrape_event()
                                    page_data["div_id"] = div_id
                                    df.loc[len(df.index)] = page_data
                                    
                                    # Save progress
                                    self._save_progress(div_id, link)
                                    processed_in_session += 1

                                # Close the current tab and switch back to the main tab
                                self._switch_back_to_main_page()
                                time.sleep(0.75)

                                if len(df.index) >= self.config.DEFAULT_MAX_LIST_SIZE:
                                    print("Reached maximum list size. Returning table...")
                                    return df
                                    
                            except (WebDriverException, TimeoutException) as e:
                                self._print_error(e)
                                # Try to recover the session
                                if not self._handle_session_error(link, div_id):
                                    raise e
                                
                                # Re-find the main div after recovery
                                main_div = self.wait.until(
                                    EC.presence_of_element_located((By.CSS_SELECTOR, "div.wrapper.row.event-search-items"))
                                )
                                break  # Break from item loop to restart with recovered session
                                
                            except Exception as e:
                                self._print_error(e)
                                self._switch_back_to_main_page()
                                time.sleep(0.75)
                                continue

                        if not self._load_more():
                            print("No more items to load.")
                            break

                        old_length = len(child_items)
                        
                    except (WebDriverException, TimeoutException) as e:
                        self._print_error(e)
                        # Try to recover the session
                        if not self._handle_session_error(link, self.last_processed_id):
                            raise e
                        
                        # Re-find the main div after recovery
                        main_div = self.wait.until(
                            EC.presence_of_element_located((By.CSS_SELECTOR, "div.wrapper.row.event-search-items"))
                        )
                        continue

                # If we reach here, scraping completed successfully
                print("Scraping completed successfully.")
                break
                
            except Exception as e:
                retry_count += 1
                self._print_error(e)
                
                if retry_count < max_retries:
                    print(f"Retrying... Attempt {retry_count + 1}/{max_retries}")
                    time.sleep(5)  # Wait before retry
                else:
                    print("Max retries reached. Returning partial results.")
                    break

        if self.driver:
            self.driver.quit()
        print("Returning table...")
        return df

    def save_data(self, df: pd.DataFrame):
        """Save scraped data to CSV file."""
        combined_df = pd.concat([self.existing_data, df], ignore_index=True)
        combined_df.to_csv(self.config.OUTPUT_FILE, index=False, sep=";", encoding="utf-8-sig")
        print("Table is saved.")
        
        # Clean up progress file after successful save
        progress_file = 'scraper_progress.txt'
        if os.path.exists(progress_file):
            try:
                os.remove(progress_file)
            except Exception:
                pass

    def run(self, url_index: int = 0):
        """Run the scraper for a specific URL index."""
        if url_index >= len(self.config.SCRAPING_URLS):
            raise ValueError(f"URL index {url_index} is out of range")

        url = self.config.SCRAPING_URLS[url_index]
        scraped_df = self.scrape(url)
        self.save_data(scraped_df)


def main():
    """Main execution function."""
    scraper = PassoScraper()
    try:
        scraper.run(url_index=0)  # Run scraper for the first URL
    except Exception as e:
        print(f"Error during scraping: {e}")
    finally:
        # Ensure driver is closed
        try:
            if scraper.driver:
                scraper.driver.quit()
        except Exception:
            pass


if __name__ == "__main__":
    main()