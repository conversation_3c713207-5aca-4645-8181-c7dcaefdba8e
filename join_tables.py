import uuid
from typing import List

import pandas as pd
from config import DataProcessingConfig, FilePathConfig
from database import DatabaseManager


class DataProcessor:
    """Handles data processing and transformation for IVent data."""

    def __init__(self):
        self.db_manager = DatabaseManager()
        self.processing_config = DataProcessingConfig()
        self.available_categories = self.db_manager.get_available_categories()

    def _date_mapper(self, date: str) -> str:
        """Convert Turkish date format to SQL timestamp format."""
        date_parts = date.split(" ")
        month_abbr = date_parts[1][:3]

        if month_abbr in self.processing_config.TURKISH_MONTH_MAP:
            date_parts[1] = self.processing_config.TURKISH_MONTH_MAP[month_abbr]
            return f"TO_TIMESTAMP('{date_parts[0]}-{date_parts[1]}-2024 {date_parts[4]}', 'DD-MM-YYYY HH24:MI')"
        else:
            raise ValueError(f"Unknown month abbreviation: {month_abbr}")

    def process_event_data(
        self,
        input_file: str,
        locations_file: str,
        output_file: str,
        date_table_file: str,
        vibe_folder_table_file: str,
        memory_folder_table_file: str,
    ) -> None:
        """Process event data by joining with locations and preparing for database insertion."""

        # Read event data
        csv = pd.read_csv(input_file, sep=";", encoding="utf-8-sig")
        csv = csv.loc[
            :,
            [
                "website_url",
                "thumbnail_url",
                "event_name",
                "date",
                "coordinates",
                "description",
            ],
        ]

        # Read location data
        locations = pd.read_csv(locations_file, sep=";", encoding="utf-8-sig")
        locations = locations.loc[:, ["id", "coordinates"]]
        locations.rename(columns={"id": "location_id"}, inplace=True)

        # Join with locations
        csv = csv.merge(locations, on="coordinates", how="left")

        # Filter and clean data
        csv = csv[~csv["location_id"].isnull()]
        csv = csv[~csv["date"].isnull()]
        csv = csv[csv["date"].str.contains(r"\D - \d{2}:\d{2}$", regex=True)]
        csv.drop(columns=["coordinates"], inplace=True)
        csv.rename(columns={"event_name": "ivent_name"}, inplace=True)
        csv.drop_duplicates(subset=["website_url"], inplace=True)

        # Assign random categories
        sample_categories = (
            self.available_categories.sample(n=len(csv), replace=True)["id"]
            .reset_index(drop=True)
            .apply(lambda x: x.lower())
            .values.tolist()
        )
        csv["category_tag_id"] = sample_categories

        # Add metadata
        csv["creator_type"] = "distributor"
        csv["creator_distributor_id"] = self.processing_config.DEFAULT_CREATOR_DISTRIBUTOR_ID
        csv["id"] = [str(uuid.uuid4()) for _ in range(len(csv))]
        csv["vibe_folder_id"] = [str(uuid.uuid4()) for _ in range(len(csv))]
        csv["memory_folder_id"] = [str(uuid.uuid4()) for _ in range(len(csv))]

        # Create date table
        date_table = csv.loc[:, ["date", "id"]].copy()
        date_table["date"] = date_table["date"].apply(self._date_mapper)
        date_table.rename(columns={"date": "ivent_date", "id": "ivent_id"}, inplace=True)

        # Remove date from main table
        csv.drop(columns=["date"], inplace=True)

        # Create vibe and memory folder tables
        vibe_folder_table = csv.loc[:, ["vibe_folder_id"]].copy()
        vibe_folder_table.rename(columns={"vibe_folder_id": "id"}, inplace=True)
        memory_folder_table = csv.loc[:, ["memory_folder_id"]].copy()
        memory_folder_table.rename(columns={"memory_folder_id": "id"}, inplace=True)

        # Save results
        csv.to_csv(output_file, sep=";", index=False, encoding="utf-8-sig")
        date_table.to_csv(date_table_file, sep=";", index=False, encoding="utf-8-sig")
        vibe_folder_table.to_csv(vibe_folder_table_file, sep=";", index=False, encoding="utf-8-sig")
        memory_folder_table.to_csv(memory_folder_table_file, sep=";", index=False, encoding="utf-8-sig")

        print(f"Processed {len(csv)} events")
        print(f"Event data saved to: {output_file}")
        print(f"Date data saved to: {date_table_file}")
        print(f"Vibe folder data saved to: {vibe_folder_table_file}")
        print(f"Memory folder data saved to: {memory_folder_table_file}")


def main():
    """Main execution function."""
    processor = DataProcessor()
    processor.process_event_data(
        input_file=FilePathConfig.PASSO_CSV,
        locations_file=FilePathConfig.LOCATIONS_CSV,
        output_file=FilePathConfig.PASSO_EDITED_CSV,
        date_table_file=FilePathConfig.IVENT_DATES_CSV,
        vibe_folder_table_file=FilePathConfig.VIBE_FOLDERS_CSV,
        memory_folder_table_file=FilePathConfig.MEMORY_FOLDERS_CSV,
    )


if __name__ == "__main__":
    main()
