import uuid
from typing import Dict, Optional

import pandas as pd
import requests
from config import FilePathConfig, MapboxConfig
from database import DatabaseManager


class MapboxClient:
    """Handles Mapbox API interactions."""

    def __init__(self):
        self.api_key = MapboxConfig.API_KEY

    def get_location_information(self, latlng: str) -> Dict[str, Optional[str]]:
        """Get location information from coordinates using Mapbox API."""
        result = {
            "place_id": None,
            "country": None,
            "city": None,
            "district": None,
            "data": None,
        }

        try:
            # Parse the latitude and longitude from the latlng string (format: "latitude,longitude")
            lat, lng = latlng.split(",")

            # Mapbox requires coordinates in the format "longitude,latitude"
            coordinates = f"{lng},{lat}"

            # Mapbox Reverse Geocoding API endpoint
            endpoint = f"https://api.mapbox.com/geocoding/v5/mapbox.places/{coordinates}.json"
            params = {
                "access_token": self.api_key,
                "limit": 1,  # Get only the most relevant result
            }
            response = requests.get(endpoint, params=params)

            if response.status_code == 200:
                data = response.json()
                result["data"] = data

                if data["features"]:
                    feature = data["features"][0]
                    country = None
                    city = None
                    district = None

                    # Extract place_id (Mapbox's id)
                    properties = feature.get("properties", {})

                    if "id" in feature:
                        place_id = feature.get("id")

                    # Process context to extract administrative areas
                    context = feature.get("context", [])
                    priority_map = {"country": "1", "region": "2", "place": "3", "locality": "4"}
                    details_dict = {v: None for v in priority_map.values()}

                    for ctx in context:
                        ctx_id = ctx.get("id", "")
                        for key, index in priority_map.items():
                            if ctx_id.startswith(key):
                                details_dict[index] = ctx.get("text")
                                break

                    details = [v for v in details_dict.values() if v is not None]
                    country, city, district = (details + [None, None, None])[:3]

                    result["place_id"] = place_id
                    result["country"] = country
                    result["city"] = city
                    result["district"] = district
                    return result
                else:
                    print(f"No results found: {latlng}")
                    return result
            else:
                print(f"Error fetching data from Mapbox API: {response.status_code}")
                return result
        except Exception as e:
            print(f"Error in get_location_information: {e}")
            return result


class LocationProcessor:
    """Processes location data and generates location records."""

    def __init__(self):
        self.db_manager = DatabaseManager()
        self.maps_client = MapboxClient()
        self.existing_locations = self.db_manager.get_existing_locations()

    def get_location_id(self, mapbox_id: str) -> str:
        """Get existing location ID or generate new one."""
        if self.existing_locations is None or self.existing_locations.empty:
            return str(uuid.uuid4())
        location_id = self.existing_locations[self.existing_locations["mapbox_id"] == mapbox_id]
        if len(location_id) > 0:
            print(f"Found existing location: {location_id['id'].values[0]}")
            return location_id["id"].values[0]
        else:
            return str(uuid.uuid4())

    def process_locations(self, input_file: str, output_file: str) -> None:
        """Process location data from input CSV and generate output CSV with location information."""
        # Read and clean input data
        csv = pd.read_csv(input_file, sep=";", encoding="utf-8-sig")
        csv = csv.loc[:, ["coordinates", "location_name", "open_address"]]
        csv.drop_duplicates(inplace=True)
        csv.dropna(inplace=True)

        coordinates = csv["coordinates"].unique()
        print(f"Processing {len(coordinates)} unique coordinates...")

        # Get location information for all coordinates
        coordinate_mapping = {}
        for coord in coordinates:
            print(f"Processing coordinate: {coord}")
            coordinate_mapping[coord] = self.maps_client.get_location_information(coord)

        # Extract mappings
        place_id_mapping = {k: v["place_id"] for k, v in coordinate_mapping.items()}
        district_mapping = {k: v["district"] for k, v in coordinate_mapping.items()}
        city_mapping = {k: v["city"] for k, v in coordinate_mapping.items()}

        # Apply mappings to dataframe
        csv["district"] = csv["coordinates"].map(district_mapping)
        csv["city"] = csv["coordinates"].map(city_mapping)
        csv["mapbox_id"] = csv["coordinates"].map(place_id_mapping)
        csv["geom"] = csv["coordinates"].apply(lambda x: f"ST_SetSRID(ST_MakePoint({x}), 4326)")

        # Remove duplicates and generate IDs
        csv.drop_duplicates(subset=["mapbox_id"], inplace=True)
        csv.insert(0, "id", csv["mapbox_id"].apply(self.get_location_id))

        # Save results
        csv.to_csv(output_file, sep=";", encoding="utf-8-sig", index=False)
        print(f"Location data saved to: {output_file}")


def main():
    """Main execution function."""
    processor = LocationProcessor()
    processor.process_locations(input_file=FilePathConfig.PASSO_CSV, output_file=FilePathConfig.LOCATIONS_CSV)


if __name__ == "__main__":
    main()
