"""
Database utilities for the IVent data processing project.
"""

from typing import Any, Dict, List

import pandas as pd
import pyodbc

from config import DatabaseConfig


class DatabaseManager:
    """Handles database connections and queries."""
    
    def __init__(self):
        self.config = DatabaseConfig()
    
    def execute_query(self, query: str) -> pd.DataFrame:
        """
        Execute a SQL query and return results as DataFrame.
        
        Args:
            query: SQL query to execute
            
        Returns:
            DataFrame with query results
        """
        try:
            with pyodbc.connect(self.config.CONNECTION_STRING) as connection:
                cursor = connection.cursor()
                cursor.execute(query)
                result = cursor.fetchall()

                if not result:
                    return pd.DataFrame()

                column_names = [column[0] for column in cursor.description]
                columns_size = len(column_names)

                dataframe = pd.DataFrame(
                    {column_names[i]: [res[i] for res in result] for i in range(columns_size)}
                )
                return dataframe
        except Exception as e:
            print(f"Database query error: {e}")
            raise
    
    def execute_non_query(self, query: str) -> None:
        """
        Execute a SQL query that doesn't return results (INSERT, UPDATE, DELETE).
        
        Args:
            query: SQL query to execute
        """
        try:
            with pyodbc.connect(self.config.CONNECTION_STRING) as connection:
                cursor = connection.cursor()
                cursor.execute(query)
                connection.commit()
                print(f"Query executed successfully: {query[:50]}...")
        except Exception as e:
            print(f"Database execution error: {e}")
            raise
    
    def get_existing_locations(self) -> pd.DataFrame:
        """Get existing locations from database."""
        query = "SELECT id, mapbox_id FROM locations"
        locations_table = self.execute_query(query)
        if not locations_table.empty:
            locations_table["id"] = locations_table["id"].apply(lambda x: x.lower())
        return locations_table
    
    def get_available_categories(self) -> pd.DataFrame:
        """Get available hobby categories from database."""
        query = "SELECT id FROM hobbies WHERE level = 2"
        return self.execute_query(query)
    
    def table_exists(self, table_name: str) -> bool:
        """
        Check if a table exists in the database.
        
        Args:
            table_name: Name of the table to check
            
        Returns:
            True if table exists, False otherwise
        """
        query = f"""
        SELECT COUNT(*) as count 
        FROM information_schema.tables 
        WHERE table_name = '{table_name}'
        """
        result = self.execute_query(query)
        return result.iloc[0]['count'] > 0 if not result.empty else False
    
    def get_table_row_count(self, table_name: str) -> int:
        """
        Get the number of rows in a table.
        
        Args:
            table_name: Name of the table
            
        Returns:
            Number of rows in the table
        """
        query = f"SELECT COUNT(*) as count FROM {table_name}"
        result = self.execute_query(query)
        return result.iloc[0]['count'] if not result.empty else 0


class SQLQueryBuilder:
    """Utility class for building SQL queries."""
    
    @staticmethod
    def build_insert_query(table_name: str, data: List[Dict[str, Any]]) -> str:
        """
        Build an INSERT query from a list of dictionaries.
        
        Args:
            table_name: Name of the target table
            data: List of dictionaries representing rows to insert
            
        Returns:
            SQL INSERT query string
        """
        if not data:
            return f"-- No data to insert into {table_name}"
        
        columns = list(data[0].keys())
        query = f"INSERT INTO {table_name} ({', '.join(columns)})\nVALUES\n"
        
        for row in data:
            values = []
            for value in row.values():
                if isinstance(value, str):
                    # Don't quote SQL functions
                    if (value.startswith("TO_TIMESTAMP") or 
                        value.startswith("ST_SetSRID") or
                        value.startswith("ST_")):
                        values.append(str(value))
                    else:
                        # Escape single quotes
                        escaped_value = value.replace("'", "''")
                        values.append(f"'{escaped_value}'")
                elif value is None:
                    values.append("NULL")
                else:
                    values.append(str(value))
            
            query += f"({', '.join(values)}),\n"
        
        # Remove trailing comma and add semicolon
        query = query[:-2] + ";"
        return query
    
    @staticmethod
    def build_update_query(table_name: str, data: Dict[str, Any], 
                          where_clause: str) -> str:
        """
        Build an UPDATE query.
        
        Args:
            table_name: Name of the target table
            data: Dictionary of column-value pairs to update
            where_clause: WHERE clause for the update
            
        Returns:
            SQL UPDATE query string
        """
        set_clauses = []
        for column, value in data.items():
            if isinstance(value, str):
                escaped_value = value.replace("'", "''")
                set_clauses.append(f"{column} = '{escaped_value}'")
            elif value is None:
                set_clauses.append(f"{column} = NULL")
            else:
                set_clauses.append(f"{column} = {value}")
        
        query = f"UPDATE {table_name} SET {', '.join(set_clauses)} WHERE {where_clause};"
        return query
    
    @staticmethod
    def build_delete_query(table_name: str, where_clause: str) -> str:
        """
        Build a DELETE query.
        
        Args:
            table_name: Name of the target table
            where_clause: WHERE clause for the deletion
            
        Returns:
            SQL DELETE query string
        """
        return f"DELETE FROM {table_name} WHERE {where_clause};"


def test_database_connection() -> bool:
    """
    Test the database connection.
    
    Returns:
        True if connection successful, False otherwise
    """
    try:
        db = DatabaseManager()
        result = db.execute_query("SELECT 1 as test")
        return not result.empty and result.iloc[0]['test'] == 1
    except Exception as e:
        print(f"Database connection test failed: {e}")
        return False


if __name__ == "__main__":
    # Test database connection
    if test_database_connection():
        print("Database connection successful!")
    else:
        print("Database connection failed!")
