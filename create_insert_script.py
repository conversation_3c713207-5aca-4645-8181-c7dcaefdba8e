import pandas as pd

import my_tools as t

INPUT_FILE = "./data/passo_edited.csv"
LOCATIONS_FILE = "./data/locations.csv"
DATE_TABLE_FILE = "./data/ivent_dates.csv"
VIBE_FOLDERS_FILE = "./data/vibe_folders.csv"
MEMORY_FOLDERS_FILE = "./data/memory_folders.csv"

OUTPUT_FILE = "./data/insert_passo_edited.sql"
LOCATIONS_OUTPUT_FILE = "./data/insert_locations.sql"
DATE_TABLE_OUTPUT_FILE = "./data/insert_ivent_dates.sql"
VIBE_FOLDERS_OUTPUT_FILE = "./data/insert_vibe_folders.sql"
MEMORY_FOLDERS_OUTPUT_FILE = "./data/insert_memory_folders.sql"


def insert_query_builder(table_name, values: list):
    query = f"INSERT INTO {table_name} ("
    query += f"{", ".join(values[0].keys())}"
    query += f")\nVALUES\n"
    for value in values:
        for k, v in value.items():
            if type(v) == str and not v.startswith("TO_TIMESTAMP") and not v.startswith("ST_SetSRID"):
                value[k] = f"'{v.replace("'", "''")}'"
        query += f"({", ".join([str(i) for i in value.values()])}),\n"
    query = query[:-2] + ";"
    return query


def main():
    print("Creating insert script for passo_edited.csv, locations.csv, and ivent_dates.csv...")
    csv = pd.read_csv(INPUT_FILE, sep=";", encoding="utf-8-sig")
    locations = pd.read_csv(LOCATIONS_FILE, sep=";", encoding="utf-8-sig")
    date_table = pd.read_csv(DATE_TABLE_FILE, sep=";", encoding="utf-8-sig")
    vibe_folders = pd.read_csv(VIBE_FOLDERS_FILE, sep=";", encoding="utf-8-sig")
    memory_folders = pd.read_csv(MEMORY_FOLDERS_FILE, sep=";", encoding="utf-8-sig")

    locations.drop(columns=["coordinates"], inplace=True)

    query1 = insert_query_builder("ivents", csv.to_dict("records"))
    query2 = insert_query_builder("locations", locations.to_dict("records"))
    query3 = insert_query_builder("ivent_dates", date_table.to_dict("records"))
    query4 = insert_query_builder("vibe_folders", vibe_folders.to_dict("records"))
    query5 = insert_query_builder("memory_folders", memory_folders.to_dict("records"))

    t.write_file(query1, OUTPUT_FILE)
    t.write_file(query2, LOCATIONS_OUTPUT_FILE)
    t.write_file(query3, DATE_TABLE_OUTPUT_FILE)
    t.write_file(query4, VIBE_FOLDERS_OUTPUT_FILE)
    t.write_file(query5, MEMORY_FOLDERS_OUTPUT_FILE)

if __name__ == "__main__":
    main()
